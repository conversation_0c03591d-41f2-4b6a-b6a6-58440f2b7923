{"cells": [{"cell_type": "markdown", "id": "8299427f", "metadata": {}, "source": ["# Random_Forest Classification Tutorial\n", "Generated: 2025-09-13T01:20:38\n", "\n", "This notebook shows a complete, minimal workflow for **Random_Forest** on a synthetic binary classification dataset with 12 features.\n", "\n", "**What you'll do:**\n", "1. Load the dataset (`rf_data.csv`) with 12 feature columns (`f1..f12`) and a `target` (0/1).\n", "2. Explore shapes and basic stats.\n", "3. Train/validation split.\n", "4. Preprocess (scaling where appropriate).\n", "5. Train a Random_Forest classifier with reasonable defaults.\n", "6. Evaluate with accuracy, precision, recall, F1, confusion matrix, ROC-AUC.\n", "7. Save the fitted model (optional).\n"]}, {"cell_type": "code", "execution_count": null, "id": "dd59c64e", "metadata": {}, "outputs": [], "source": ["# Setup\n", "import pandas as pd\n", "import numpy as np\n", "\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix, classification_report, RocCurveDisplay\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "# Load data\n", "data_path = \"rf_data.csv\"\n", "df = pd.read_csv(data_path)\n", "\n", "print(\"Shape:\", df.shape)\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5dc004e0", "metadata": {}, "outputs": [], "source": ["# Basic EDA\n", "print(df.describe().T)\n", "print(\"\\nClass distribution:\\n\", df['target'].value_counts(normalize=True).rename('proportion'))"]}, {"cell_type": "code", "execution_count": null, "id": "2cce95b7", "metadata": {}, "outputs": [], "source": ["# Train/Validation split\n", "X = df.drop(columns=['target'])\n", "y = df['target']\n", "\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    X, y, test_size=0.2, random_state=123, stratify=y\n", ")\n", "\n", "X_train.shape, X_val.shape"]}, {"cell_type": "code", "execution_count": null, "id": "b4aef522", "metadata": {}, "outputs": [], "source": ["# Random Forest (ensemble of decision trees)\n", "from sklearn.ensemble import RandomForestClassifier\n", "\n", "clf = RandomForestClassifier(\n", "    n_estimators=300,\n", "    max_depth=None,\n", "    min_samples_split=10,\n", "    random_state=123,\n", "    n_jobs=-1\n", ")\n", "clf.fit(X_train, y_train)\n", "y_pred = clf.predict(X_val)\n", "y_proba = clf.predict_proba(X_val)[:, 1]"]}, {"cell_type": "code", "execution_count": null, "id": "035b089c", "metadata": {}, "outputs": [], "source": ["# Evaluation\n", "acc = accuracy_score(y_val, y_pred)\n", "prec = precision_score(y_val, y_pred)\n", "rec = recall_score(y_val, y_pred)\n", "f1 = f1_score(y_val, y_pred)\n", "auc = roc_auc_score(y_val, y_proba)\n", "\n", "print(f\"Accuracy : {acc:.3f}\")\n", "print(f\"Precision: {prec:.3f}\")\n", "print(f\"Recall   : {rec:.3f}\")\n", "print(f\"F1-score : {f1:.3f}\")\n", "print(f\"ROC AUC  : {auc:.3f}\")\n", "\n", "print(\"\\nClassification Report:\\n\", classification_report(y_val, y_pred))\n", "\n", "# Confusion matrix\n", "cm = confusion_matrix(y_val, y_pred)\n", "print(\"\\nConfusion Matrix:\\n\", cm)\n", "\n", "# ROC Curve (uses matplotlib; no style or color specified)\n", "RocCurveDisplay.from_predictions(y_val, y_proba)\n", "plt.title(\"ROC Curve\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "8557b20c", "metadata": {}, "outputs": [], "source": ["# (Optional) Save model with joblib for later reuse\n", "# Uncomment to persist\n", "# import joblib\n", "# joblib.dump(clf if 'clf' in globals() else pipe, \"model.joblib\")\n"]}], "metadata": {}, "nbformat": 4, "nbformat_minor": 5}