#!/usr/bin/env python3
"""
Complete KNN Classification Tutorial Test Script
This script runs the entire KNN workflow including the evaluation section.
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, 
    roc_auc_score, confusion_matrix, classification_report, 
    RocCurveDisplay
)
from sklearn.preprocessing import StandardScaler
from sklearn.neighbors import KNeighborsClassifier
from sklearn.pipeline import Pipeline
import matplotlib.pyplot as plt

def main():
    print("=" * 60)
    print("KNN Classification Tutorial - Complete Test")
    print("=" * 60)
    
    # 1. Load data
    print("\n1. Loading data...")
    try:
        data_path = "knn_data.csv"
        df = pd.read_csv(data_path)
        print(f"✓ Data loaded successfully!")
        print(f"  Shape: {df.shape}")
        print(f"  Columns: {list(df.columns)}")
    except Exception as e:
        print(f"✗ Error loading data: {e}")
        return
    
    # 2. Basic EDA
    print("\n2. Basic Exploratory Data Analysis...")
    print(f"  Dataset shape: {df.shape}")
    print(f"  Features: {df.columns[:-1].tolist()}")
    print(f"  Target: {df.columns[-1]}")
    
    print("\n  Class distribution:")
    class_dist = df['target'].value_counts(normalize=True)
    for class_val, proportion in class_dist.items():
        print(f"    Class {class_val}: {proportion:.3f} ({proportion*100:.1f}%)")
    
    # 3. Train/Validation split
    print("\n3. Splitting data...")
    X = df.drop(columns=['target'])
    y = df['target']
    
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=0.2, random_state=123, stratify=y
    )
    
    print(f"  Training set: {X_train.shape}")
    print(f"  Validation set: {X_val.shape}")
    
    # 4. Create and train model
    print("\n4. Training KNN model...")
    try:
        # Create pipeline with scaling and KNN
        pipe = Pipeline([
            ("scaler", StandardScaler()),
            ("clf", KNeighborsClassifier(n_neighbors=7))
        ])
        
        # Train the model
        pipe.fit(X_train, y_train)
        print("  ✓ Model trained successfully!")
        
        # Make predictions
        y_pred = pipe.predict(X_val)
        y_proba = pipe.predict_proba(X_val)[:, 1]
        print("  ✓ Predictions generated successfully!")
        
    except Exception as e:
        print(f"  ✗ Error training model: {e}")
        return
    
    # 5. EVALUATION SECTION
    print("\n5. Model Evaluation...")
    print("=" * 40)
    
    try:
        # Calculate metrics
        acc = accuracy_score(y_val, y_pred)
        prec = precision_score(y_val, y_pred)
        rec = recall_score(y_val, y_pred)
        f1 = f1_score(y_val, y_pred)
        auc = roc_auc_score(y_val, y_proba)
        
        # Print metrics
        print(f"Accuracy : {acc:.3f}")
        print(f"Precision: {prec:.3f}")
        print(f"Recall   : {rec:.3f}")
        print(f"F1-score : {f1:.3f}")
        print(f"ROC AUC  : {auc:.3f}")
        
        print("\nClassification Report:")
        print(classification_report(y_val, y_pred))
        
        # Confusion matrix
        cm = confusion_matrix(y_val, y_pred)
        print("Confusion Matrix:")
        print(cm)
        
        print("\n✓ All evaluation metrics calculated successfully!")
        
    except Exception as e:
        print(f"✗ Error in evaluation: {e}")
        return
    
    # 6. ROC Curve plotting
    print("\n6. Generating ROC Curve...")
    try:
        # Set backend for headless environment
        plt.switch_backend('Agg')
        
        # Create ROC curve
        fig, ax = plt.subplots(figsize=(8, 6))
        RocCurveDisplay.from_predictions(y_val, y_proba, ax=ax)
        plt.title("ROC Curve - KNN Classifier")
        plt.grid(True, alpha=0.3)
        
        # Save the plot
        plt.savefig('knn_roc_curve.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("  ✓ ROC curve saved as 'knn_roc_curve.png'")
        
    except Exception as e:
        print(f"  ✗ Error plotting ROC curve: {e}")
    
    # 7. Summary
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print(f"✓ Data loaded: {df.shape[0]} samples, {df.shape[1]-1} features")
    print(f"✓ Model trained: KNN with k=7")
    print(f"✓ Validation accuracy: {acc:.3f}")
    print(f"✓ ROC AUC: {auc:.3f}")
    print("✓ All evaluation metrics computed successfully!")
    print("\nThe evaluation section is working perfectly! 🎉")

if __name__ == "__main__":
    main()
